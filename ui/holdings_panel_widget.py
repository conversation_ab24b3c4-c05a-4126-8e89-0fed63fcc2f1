import time
import logging
from typing import List, Dict
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QListWidget, QListWidgetItem, 
                             QLabel, QSizePolicy, QHBoxLayout, QPushButton, QFrame)
from PyQt5.QtCore import pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QColor, QFont
from PyQt5.QtWidgets import QApplication

from config import PORTFOLIO_CONFIG
from okx_dex_client import OKXDexClient, AllTokenBalancesRequest, TotalValueRequest, QuoteRequest, SwapRequest
from ui.portfolio_monitor import PortfolioMonitor
from api_service import APIService

logger = logging.getLogger(__name__)

class HoldingItemWidget(QWidget):
    """持仓项目的自定义Widget"""
    def __init__(self, holding_data: Dict, total_value: float = 0.0):
        super().__init__()
        self.holding_data = holding_data
        self.total_value = total_value
        # 🔥🔥 新增：保存对标签的引用，用于后续更新
        self.strategy_label = None
        self.time_label = None
        self.strategy_timestamp = None  # 存储策略信号的时间戳
        self._setup_ui()
    
    def _setup_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(10)
        
        # 提取数据
        symbol = self.holding_data.get('symbol', 'N/A')
        name = self.holding_data.get('name', 'N/A')
        quantity = self.holding_data.get('quantity', 0.0)
        value_usd = self.holding_data.get('value_usd', 0.0)
        
        # 计算占比
        if self.total_value > 0:
            percentage = (value_usd / self.total_value) * 100
        else:
            percentage = 0.0
        
        # 符号标签 (固定宽度)
        symbol_label = QLabel(symbol)
        symbol_label.setFixedWidth(60)
        symbol_label.setStyleSheet("font-weight: bold; color: #3498db;")
        layout.addWidget(symbol_label)
        
        # 名称标签 (可拉伸)
        name_label = QLabel(name)
        name_label.setStyleSheet("color: #ecf0f1;")
        layout.addWidget(name_label, 1)  # stretch=1
        
        # 数量标签 (固定宽度)
        quantity_label = QLabel(f"{quantity:.4f}" if quantity < 1000 else f"{quantity:,.0f}")
        quantity_label.setFixedWidth(80)
        quantity_label.setAlignment(Qt.AlignRight)
        quantity_label.setStyleSheet("color: #f39c12;")
        layout.addWidget(quantity_label)
        
        # 价值标签 (固定宽度)
        value_label = QLabel(f"${value_usd:.2f}")
        value_label.setFixedWidth(80)
        value_label.setAlignment(Qt.AlignRight)
        value_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        layout.addWidget(value_label)
        
        # 占比标签 (固定宽度)
        percentage_label = QLabel(f"{percentage:.2f}%")
        percentage_label.setFixedWidth(60)
        percentage_label.setAlignment(Qt.AlignRight)
        percentage_label.setStyleSheet("color: #e74c3c;")
        layout.addWidget(percentage_label)
        
        # 🔥🔥 策略标签 (固定宽度) - 保存引用
        self.strategy_label = QLabel("--")
        self.strategy_label.setFixedWidth(50)
        self.strategy_label.setAlignment(Qt.AlignCenter)
        self.strategy_label.setStyleSheet("color: #95a5a6;")
        layout.addWidget(self.strategy_label)
        
        # 🔥🔥 时间标签 (固定宽度) - 保存引用
        self.time_label = QLabel("--")
        self.time_label.setFixedWidth(60)
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setStyleSheet("color: #95a5a6; font-size: 10px;")
        layout.addWidget(self.time_label)
        
        # 设置整体样式
        self.setStyleSheet("""
            HoldingItemWidget {
                background-color: #34495e;
                border: 1px solid #2c3e50;
                border-radius: 4px;
                margin: 1px;
            }
            HoldingItemWidget:hover {
                background-color: #3498db;
                border-color: #2980b9;
            }
        """)
        
        # 设置固定高度
        self.setFixedHeight(32)
    
    def update_strategy(self, signal_type: str):
        """更新策略信号显示"""
        if self.strategy_label:
            # 设置策略信号文本
            self.strategy_label.setText(signal_type)
            
            # 根据信号类型设置颜色
            if signal_type.upper() in ['BUY', '买入']:
                color = "#27ae60"  # 绿色
            elif signal_type.upper() in ['SELL', '卖出']:
                color = "#e74c3c"  # 红色
            elif signal_type.upper() in ['HOLD', '持有']:
                color = "#f39c12"  # 橙色
            else:
                color = "#95a5a6"  # 灰色
            
            self.strategy_label.setStyleSheet(f"color: {color}; font-weight: bold;")
            
            # 记录策略更新时间
            import time
            self.strategy_timestamp = time.time()
            self.update_time_display()
    
    def update_time_display(self):
        """更新时间显示"""
        if self.time_label and self.strategy_timestamp:
            import time
            current_time = time.time()
            diff_seconds = int(current_time - self.strategy_timestamp)
            
            if diff_seconds < 60:
                time_text = f"{diff_seconds}s"
            elif diff_seconds < 3600:  # 小于1小时
                minutes = diff_seconds // 60
                time_text = f"{minutes}m"
            elif diff_seconds < 86400:  # 小于1天
                hours = diff_seconds // 3600
                time_text = f"{hours}h"
            else:  # 超过1天
                days = diff_seconds // 86400
                time_text = f"{days}d"
            
            self.time_label.setText(time_text)
        elif self.time_label:
            self.time_label.setText("--")

class HoldingsPanelWidget(QWidget):
    """
    持仓信息展示面板 - 使用QListWidget替代QTableWidget
    """
    holding_selected = pyqtSignal(dict)  # 当用户点击某个持仓时发出信号，参数为该持仓的token_info字典

    def __init__(self, parent=None):
        super().__init__(parent)
        self.holdings_data = []  # 存储原始持仓数据
        self.total_portfolio_value = 0.0
        
        # 🔥🔥 新增：添加定时器用于更新时间显示
        from PyQt5.QtCore import QTimer
        self.time_update_timer = QTimer()
        self.time_update_timer.timeout.connect(self.update_all_holdings_relative_times)
        self.time_update_timer.start(5000)  # 每5秒更新一次时间显示
        
        self._init_ui()

    def _init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)

        # 🔥🔥 标题栏布局，包含标题和修复按钮
        title_layout = QHBoxLayout()
        
        self.title_label = QLabel("持仓 (新版)")
        self.title_label.setStyleSheet("font-weight: bold; padding: 5px; background-color: #2c3e50; color: #ecf0f1;")
        self.title_label.setMinimumHeight(25)
        title_layout.addWidget(self.title_label)
        
        # 修复显示按钮（暂时保留，但在ListWidget版本中应该不需要）
        self.fix_display_btn = QPushButton("刷新")
        self.fix_display_btn.setMaximumSize(50, 25)
        self.fix_display_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        self.fix_display_btn.clicked.connect(self.refresh_display)
        title_layout.addWidget(self.fix_display_btn)
        
        layout.addLayout(title_layout)

        # 🔥🔥 添加列标题
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(8, 4, 8, 4)
        header_layout.setSpacing(10)
        
        # 创建列标题
        headers = [
            ("符号", 60),
            ("名称", 1),  # stretch
            ("数量", 80),
            ("价值", 80),
            ("占比", 60),
            ("策略", 50),
            ("时间", 60)
        ]
        
        for header_text, width in headers:
            header_label = QLabel(header_text)
            header_label.setStyleSheet("""
                QLabel {
                    color: #ecf0f1;
                    font-weight: bold;
                    font-size: 11px;
                    background-color: #34495e;
                    padding: 4px;
                    border: 1px solid #2c3e50;
                }
            """)
            if width == 1:  # stretch
                header_layout.addWidget(header_label, 1)
            else:
                header_label.setFixedWidth(width)
                header_label.setAlignment(Qt.AlignCenter)
                header_layout.addWidget(header_label)
        
        layout.addWidget(header_widget)

        # 🔥🔥 新的列表控件
        self.list_widget = QListWidget()
        self.list_widget.setStyleSheet("""
            QListWidget {
                background-color: #2c3e50;
                border: 1px solid #34495e;
                outline: none;
            }
            QListWidget::item {
                border: none;
                padding: 0px;
                margin: 1px;
            }
            QListWidget::item:selected {
                background-color: transparent;
            }
        """)
        
        # 连接点击事件
        self.list_widget.itemClicked.connect(self.on_item_clicked)
        
        layout.addWidget(self.list_widget)

    def get_relative_time(self, timestamp: float) -> str:
        """计算相对时间显示，如'5s ago'、'2m ago'等"""
        try:
            current_time = time.time()
            diff_seconds = int(current_time - timestamp)
            
            if diff_seconds < 60:
                return f"{diff_seconds}s ago"
            elif diff_seconds < 3600:  # 小于1小时
                minutes = diff_seconds // 60
                return f"{minutes}m ago"
            elif diff_seconds < 86400:  # 小于1天
                hours = diff_seconds // 3600
                return f"{hours}h ago"
            else:  # 超过1天
                days = diff_seconds // 86400
                return f"{days}d ago"
        except Exception as e:
            logger.error(f"计算相对时间失败: {e}")
            return "--"

    def format_value(self, value, decimals=2, is_percentage=False, return_float=False):
        """格式化数值以便显示，处理None和错误。"""
        if value is None:
            return "N/A" if not return_float else 0.0
        try:
            val_float = float(value)
            if return_float:
                return val_float
            if is_percentage:
                return f"{val_float:.{decimals}f}%"
            return f"{val_float:,.{decimals}f}"
        except (ValueError, TypeError):
            return "N/A" if not return_float else 0.0

    def update_holdings(self, holdings_list: list):
        """更新持仓显示 - 新版本使用QListWidget"""
        print(f"[DEBUG HoldingsPanelWidget.update_holdings] === 开始更新持仓 (ListWidget版本) ===")
        print(f"[DEBUG HoldingsPanelWidget.update_holdings] Received data count: {len(holdings_list)}")
        
        # 🔥 修复：当接收到空数据时，不清空现有显示，保持当前状态
        if not holdings_list:
            print(f"[INFO HoldingsPanelWidget.update_holdings] 收到空持仓数据，保留当前显示内容")
            return
        
        # 🔥 保存数据供其他方法使用
        self.holdings_data = holdings_list.copy()
        
        # 🔥 按价值排序并限制显示前50个币
        holdings_with_value = []
        for idx, holding_data in enumerate(holdings_list):
            value_usd_str = holding_data.get('value_usd', '0')
            try:
                value_usd_float = float(value_usd_str) if value_usd_str else 0.0
            except (ValueError, TypeError):
                value_usd_float = 0.0
            holdings_with_value.append({
                'data': holding_data, 
                'original_idx': idx,
                'value_for_sort': value_usd_float
            })
        
        # 按价值排序
        sorted_holdings = sorted(holdings_with_value, key=lambda x: x['value_for_sort'], reverse=True)
        
        # 限制显示前50个
        display_count = min(50, len(sorted_holdings))
        display_holdings = sorted_holdings[:display_count]
        
        print(f"[INFO HoldingsPanelWidget] 持仓币种数量: {len(holdings_list)}，仅显示价值排名前 {display_count} 个")
        
        # 计算总价值
        self.total_portfolio_value = sum(h['value_for_sort'] for h in sorted_holdings)
        
        # 清空现有显示
        self.list_widget.clear()
        
        # 添加新的持仓项目
        for item_data in display_holdings:
            holding_data = item_data['data']
            
            # 创建自定义widget
            holding_widget = HoldingItemWidget(holding_data, self.total_portfolio_value)
            
            # 创建list item
            list_item = QListWidgetItem(self.list_widget)
            list_item.setSizeHint(holding_widget.sizeHint())
            
            # 存储原始数据到item中
            list_item.setData(Qt.UserRole, holding_data)
            
            # 将widget设置到list item
            self.list_widget.setItemWidget(list_item, holding_widget)
        
        print(f"[DEBUG HoldingsPanelWidget.update_holdings] === 完成更新持仓 === 显示项目数: {self.list_widget.count()}")

    def on_item_clicked(self, item: QListWidgetItem):
        """处理持仓项目点击事件"""
        if item is None:
            return
            
        # 获取存储的原始数据
        holding_data = item.data(Qt.UserRole)
        if holding_data:
            print(f"[DEBUG HoldingsPanelWidget.on_item_clicked] Clicked holding: {holding_data.get('symbol', 'N/A')}")
            # 发射信号，传递持仓信息
            self.holding_selected.emit(holding_data)

    def refresh_display(self):
        """刷新显示 - 重新渲染当前数据"""
        print("[DEBUG HoldingsPanelWidget.refresh_display] 手动刷新显示...")
        if self.holdings_data:
            current_data = self.holdings_data.copy()
            self.update_holdings(current_data)
        else:
            print("[DEBUG HoldingsPanelWidget.refresh_display] 无数据可刷新")

    def clear_holdings(self):
        """清空持仓显示"""
        print("[DEBUG HoldingsPanelWidget.clear_holdings] 清空持仓显示")
        self.list_widget.clear()
        self.holdings_data = []
        self.total_portfolio_value = 0.0

    def update_holdings_table_strategy_signal(self, token_address: str, token_symbol: str, signal_type: str):
        """更新持仓表格中的策略信号显示 - ListWidget版本"""
        
        # 遍历所有列表项目更新策略信号
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if item:
                holding_data = item.data(Qt.UserRole)
                if holding_data and holding_data.get('address') == token_address:
                    # 获取当前的widget并更新策略显示
                    widget = self.list_widget.itemWidget(item)
                    if widget and hasattr(widget, 'update_strategy'):
                        widget.update_strategy(signal_type)
                    # print(f"[DEBUG] 更新了 {token_symbol} 的策略信号: {signal_type}")
                    break

    def update_all_holdings_relative_times(self):
        """更新所有持仓的相对时间显示 - ListWidget版本"""
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if item:
                widget = self.list_widget.itemWidget(item)
                if widget and hasattr(widget, 'update_time_display'):
                    widget.update_time_display()

    # 保留一些兼容性方法（空实现）
    def check_table_integrity(self, context: str = "unknown"):
        """兼容性方法 - ListWidget版本不需要完整性检查"""
        return self.list_widget.count(), len(self.holdings_data)

    def reset_table_styles(self):
        """兼容性方法 - ListWidget版本不需要样式重置"""
        print("[DEBUG reset_table_styles] ListWidget版本不需要样式重置")

    def force_display_fix(self):
        """兼容性方法 - 直接调用刷新显示"""
        self.refresh_display()
