"""
Portfolio 监控管理器 - 管理钱包持仓代币的策略信号监控
使用HeadlessChartWidget后台监控价值大于1美元的持仓代币
"""

import logging
from typing import Dict, List, Optional, Set
from datetime import datetime
import copy

from PyQt5.QtCore import QObject, QTimer, pyqtSignal, pyqtSlot

from api_service import APIService
from ui.headless_chart_widget import HeadlessChartWidget
from config import PORTFOLIO_CONFIG

logger = logging.getLogger('portfolio_monitor')


class PortfolioMonitor(QObject):
    """Portfolio监控管理器 - 管理钱包持仓代币的策略监控"""
    
    # 信号定义 - 转发来自HeadlessChartWidget的信号
    portfolio_signal_generated = pyqtSignal(str, str, float, int, str, int)  # token_symbol, 信号类型, 价格, 时间戳, 策略名, 索引
    portfolio_analysis_completed = pyqtSignal(str, str, str)  # token_address, token_symbol, final_signal
    monitoring_status_changed = pyqtSignal(str, int, int)  # status_message, active_count, total_count
    
    def __init__(self, api_service: APIService, parent=None):
        """
        初始化Portfolio监控管理器
        
        参数:
            api_service (APIService): APIService实例
            parent (QObject, optional): 父对象
        """
        super().__init__(parent)
        self.api_service = api_service
        
        # 监控实例管理
        self.headless_widgets: Dict[str, HeadlessChartWidget] = {}  # token_address -> HeadlessChartWidget
        self.monitored_tokens: Dict[str, Dict] = {}  # token_address -> token_data
        
        # 配置参数
        self.min_token_value = PORTFOLIO_CONFIG.get("min_monitor_value", 1.0)  # 最小监控价值（美元）
        self.default_strategy = PORTFOLIO_CONFIG.get("default_strategy", "VWAP 交叉策略")  # 默认策略
        self.default_timeframe = PORTFOLIO_CONFIG.get("default_timeframe", "1m")  # 默认时间周期
        
        # 监控状态
        self.is_active = False
        self.total_monitored = 0
        self.active_monitored = 0
        
        # 状态更新定时器
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.update_monitoring_status)
        # 🔥 不立即启动定时器，等待start_monitoring被调用后再启动
        # self.status_timer.start(10000)  # 每10秒更新一次状态
        
        logger.info(f"PortfolioMonitor: 初始化完成，最小监控价值: ${self.min_token_value}")
    
    def start_monitoring(self, token_balances: List[Dict]):
        """
        开始监控钱包持仓代币
        
        参数:
            token_balances (List[Dict]): 代币余额列表
        """
        try:
            logger.info(f"PortfolioMonitor: 开始分析持仓代币...")
            
            # 🔥 立即发射开始状态
            self.monitoring_status_changed.emit("正在分析持仓代币...", 0, 0)
            
            # 筛选符合监控条件的代币
            eligible_tokens = self.filter_eligible_tokens(token_balances)
            
            if not eligible_tokens:
                logger.info(f"PortfolioMonitor: 没有符合监控条件的代币（价值需大于${self.min_token_value}）")
                self.stop_monitoring()
                # 🔥 发射无代币状态
                self.monitoring_status_changed.emit(f"无符合条件的代币（需>${self.min_token_value}美元）", 0, 0)
                return
            
            logger.info(f"PortfolioMonitor: 找到 {len(eligible_tokens)} 个符合监控条件的代币")
            
            # 🔥 发射找到代币状态
            self.monitoring_status_changed.emit(f"找到{len(eligible_tokens)}个代币，启动监控中...", 0, len(eligible_tokens))
            
            # 获取当前监控的代币地址集合
            current_monitored = set(self.monitored_tokens.keys())
            new_monitored = set(token['tokenAddress'] for token in eligible_tokens)
            
            # 计算需要添加和移除的代币
            tokens_to_add = new_monitored - current_monitored
            tokens_to_remove = current_monitored - new_monitored
            
            # 移除不再需要监控的代币
            for token_address in tokens_to_remove:
                self.remove_token_monitoring(token_address)
            
            # 添加新的监控代币
            for token in eligible_tokens:
                token_address = token['tokenAddress']
                if token_address in tokens_to_add:
                    self.add_token_monitoring(token)
                else:
                    # 更新现有代币的数据
                    self.update_token_data(token)
            
            self.is_active = True
            self.total_monitored = len(self.monitored_tokens)
            
            # 🔥 启动状态更新定时器
            if not self.status_timer.isActive():
                self.status_timer.start(10000)  # 每10秒更新一次状态
            
            # 🔥 立即更新并发射最终状态
            self.update_monitoring_status()
            
            logger.info(f"PortfolioMonitor: 监控启动完成，正在监控 {self.total_monitored} 个代币")
            
        except Exception as e:
            logger.error(f"PortfolioMonitor: 启动监控时出错: {e}", exc_info=True)
            # 🔥 发射错误状态
            self.monitoring_status_changed.emit("监控启动失败", 0, 0)
    
    def filter_eligible_tokens(self, token_balances: List[Dict]) -> List[Dict]:
        """
        筛选符合监控条件的代币
        
        参数:
            token_balances (List[Dict]): 代币余额列表
            
        返回:
            List[Dict]: 符合条件的代币列表
        """
        eligible_tokens = []
        
        for token in token_balances:
            try:
                # 获取代币基本信息
                symbol = token.get('symbol', '')  # 🔥 FIXED: 移除.upper()，保持原始大小写
                token_address = token.get('tokenContractAddress', '')
                balance = float(token.get('balance', 0))
                price = float(token.get('tokenPrice', 0))
                value = balance * price
                is_risk_token = token.get('isRiskToken', False)
                
                # 跳过无效数据
                if not token_address or not symbol:
                    continue
                
                # 检查价值条件
                if value < self.min_token_value:
                    logger.debug(f"PortfolioMonitor: {symbol} 价值 ${value:.6f} 低于最小监控值 ${self.min_token_value}")
                    continue
                
                # 跳过风险代币（可配置）
                if is_risk_token and not PORTFOLIO_CONFIG.get("monitor_risk_tokens", False):
                    logger.debug(f"PortfolioMonitor: {symbol} 是风险代币，跳过监控")
                    continue
                
                # 跳过主要稳定币作为交易对（通常不需要策略监控）
                if symbol in ['USDC', 'USDT', 'BUSD'] and not PORTFOLIO_CONFIG.get("monitor_stablecoins", False):
                    logger.debug(f"PortfolioMonitor: {symbol} 是稳定币，跳过监控")
                    continue
                
                # 构建监控用的代币数据
                monitor_token = {
                    'symbol': symbol,
                    'name': token.get('tokenName', symbol),
                    'tokenAddress': token_address,  # 使用统一的字段名，HeadlessChartWidget期望此字段
                    'address': token_address,  # 为兼容性添加address字段  
                    'price': price,
                    'balance': balance,
                    'calculated_value': value,
                    'source': 'trend',  # 🔥 修正：使用'trend'以便使用Birdeye API获取K线数据
                    'strategy_name': self.default_strategy,
                    'timeframe': self.default_timeframe,
                    'is_risk_token': is_risk_token
                }
                
                eligible_tokens.append(monitor_token)
                logger.info(f"PortfolioMonitor: 符合条件 - {symbol} (${value:.2f})")
                
            except (ValueError, TypeError) as e:
                logger.warning(f"PortfolioMonitor: 处理代币数据时出错: {e}")
                continue
        
        # 按价值排序，优先监控高价值代币
        eligible_tokens.sort(key=lambda x: x['calculated_value'], reverse=True)
        
        # 限制监控数量（避免过多监控影响性能）
        max_monitor_count = PORTFOLIO_CONFIG.get("max_monitor_count", 20)
        if len(eligible_tokens) > max_monitor_count:
            logger.info(f"PortfolioMonitor: 代币数量超过限制，只监控价值最高的 {max_monitor_count} 个")
            eligible_tokens = eligible_tokens[:max_monitor_count]
        
        return eligible_tokens
    
    def add_token_monitoring(self, token_data: Dict):
        """
        添加代币监控
        
        参数:
            token_data (Dict): 代币数据
        """
        try:
            # 🔥 创建token_data的深拷贝，避免共享引用问题
            token_data_copy = copy.deepcopy(token_data)
            
            token_address = token_data_copy['tokenAddress']
            symbol = token_data_copy['symbol']
            value = token_data_copy['calculated_value']
            
            # 🔥 添加详细的调试信息
            logger.info(f"PortfolioMonitor: 添加监控 - 代币符号: {symbol}, 代币地址: {token_address}, 价值: ${value:.2f}")
            logger.debug(f"PortfolioMonitor: 代币数据内容: {token_data_copy}")
            
            # 创建HeadlessChartWidget实例
            widget_id = f"Portfolio_{symbol}_{token_address[:8]}"
            headless_widget = HeadlessChartWidget(
                api_service=self.api_service,
                widget_id=widget_id,
                parent=self
            )
            
            # 连接信号
            headless_widget.trade_signal_generated.connect(self.on_signal_generated)
            headless_widget.strategy_analysis_completed.connect(self.on_analysis_completed)
            
            # 存储实例和数据（使用副本）
            self.headless_widgets[token_address] = headless_widget
            self.monitored_tokens[token_address] = token_data_copy
            
            # 🔥 在设置代币前记录关键信息
            logger.info(f"PortfolioMonitor: 准备为 {widget_id} 设置代币，地址: {token_address}")
            
            # 设置代币开始监控（使用副本）
            headless_widget.set_token(token_data_copy)
            
            logger.info(f"PortfolioMonitor: 开始监控 {symbol} (${value:.2f}) [{widget_id}]")
            
        except Exception as e:
            logger.error(f"PortfolioMonitor: 添加代币监控失败 {token_data.get('symbol', 'Unknown')}: {e}", exc_info=True)
    
    def remove_token_monitoring(self, token_address: str):
        """
        移除代币监控
        
        参数:
            token_address (str): 代币地址
        """
        try:
            if token_address in self.headless_widgets:
                widget = self.headless_widgets[token_address]
                token_data = self.monitored_tokens.get(token_address, {})
                symbol = token_data.get('symbol', 'Unknown')
                
                # 停止监控组件
                widget.stop_activity()
                widget.clear_data()
                
                # 断开信号连接
                try:
                    widget.trade_signal_generated.disconnect()
                    widget.strategy_analysis_completed.disconnect()
                except TypeError:
                    pass  # 信号可能已经断开
                
                # 删除实例
                widget.deleteLater()
                del self.headless_widgets[token_address]
                del self.monitored_tokens[token_address]
                
                logger.info(f"PortfolioMonitor: 停止监控 {symbol}")
                
        except Exception as e:
            logger.error(f"PortfolioMonitor: 移除代币监控失败 {token_address}: {e}")
    
    def update_token_data(self, token_data: Dict):
        """
        更新现有代币的数据
        
        参数:
            token_data (Dict): 新的代币数据
        """
        try:
            # 🔥 创建token_data的深拷贝，避免共享引用问题
            token_data_copy = copy.deepcopy(token_data)
            token_address = token_data_copy['tokenAddress']
            
            if token_address in self.monitored_tokens:
                # 更新存储的数据（使用副本）
                old_data = self.monitored_tokens[token_address]
                self.monitored_tokens[token_address] = token_data_copy
                
                # 检查是否需要更新监控组件
                if (old_data.get('price') != token_data_copy.get('price') or 
                    old_data.get('balance') != token_data_copy.get('balance')):
                    
                    # 价格或余额发生变化，重新设置代币数据（使用副本）
                    widget = self.headless_widgets[token_address]
                    widget.set_token(token_data_copy)
                    
                    symbol = token_data_copy['symbol']
                    value = token_data_copy['calculated_value']
                    logger.debug(f"PortfolioMonitor: 更新监控数据 {symbol} (${value:.2f})")
                
        except Exception as e:
            logger.error(f"PortfolioMonitor: 更新代币数据失败: {e}")
    
    @pyqtSlot(str, float, int, str, int)
    def on_signal_generated(self, signal_type: str, price: float, timestamp: int, strategy_name: str, index: int):
        """
        处理来自HeadlessChartWidget的交易信号
        
        参数:
            signal_type (str): 信号类型 ('buy' or 'sell')
            price (float): 价格
            timestamp (int): 时间戳
            strategy_name (str): 策略名称
            index (int): 索引
        """
        try:
            # 通过sender()获取发射信号的HeadlessChartWidget
            sender_widget = self.sender()
            
            # 找到对应的代币符号
            token_symbol = "Unknown"
            for token_address, widget in self.headless_widgets.items():
                if widget == sender_widget:
                    token_data = self.monitored_tokens.get(token_address, {})
                    token_symbol = token_data.get('symbol', 'Unknown')
                    break
            
            # 转发信号并添加代币符号
            self.portfolio_signal_generated.emit(
                token_symbol, signal_type, price, timestamp, strategy_name, index
            )
            
            # 记录信号
            signal_type_zh = "买入" if signal_type == 'buy' else "卖出"
            timestamp_str = datetime.fromtimestamp(timestamp).strftime('%H:%M:%S')
            logger.info(f"PortfolioMonitor: 📊 {token_symbol} {signal_type_zh}信号 @ ${price:.6f} ({timestamp_str}) - {strategy_name}")
            
        except Exception as e:
            logger.error(f"PortfolioMonitor: 处理交易信号时出错: {e}")
    
    @pyqtSlot(str, str, str)
    def on_analysis_completed(self, token_address: str, token_symbol: str, final_signal: str):
        """
        处理来自HeadlessChartWidget的分析完成信号
        
        参数:
            token_address (str): 代币地址
            token_symbol (str): 代币符号
            final_signal (str): 最终信号状态
        """
        try:
            # 转发信号
            self.portfolio_analysis_completed.emit(token_address, token_symbol, final_signal)
            
            # 记录分析结果
            logger.info(f"PortfolioMonitor: 📈 {token_symbol} 分析完成 -> {final_signal}")
            
        except Exception as e:
            logger.error(f"PortfolioMonitor: 处理分析完成信号时出错: {e}")
    
    def update_monitoring_status(self):
        """更新监控状态"""
        try:
            # 计算活跃监控数量
            active_count = 0
            for widget in self.headless_widgets.values():
                if widget.df is not None and not widget.df.empty:
                    active_count += 1
            
            self.active_monitored = active_count
            self.total_monitored = len(self.monitored_tokens)
            
            # 构建状态消息
            if self.is_active:
                if self.total_monitored > 0:
                    status_msg = f"监控 {self.active_monitored}/{self.total_monitored} 个持仓代币"
                else:
                    status_msg = "无符合条件的持仓代币"
            else:
                status_msg = "Portfolio监控未启动"
            
            # 发射状态信号
            self.monitoring_status_changed.emit(status_msg, self.active_monitored, self.total_monitored)
            
            # 记录详细状态（较低频率）
            if self.total_monitored > 0:
                logger.debug(f"PortfolioMonitor: 状态更新 - {status_msg}")
                
                # 每分钟记录一次详细信息
                current_time = datetime.now()
                if current_time.second == 0:  # 整点分钟
                    monitored_symbols = [
                        f"{data.get('symbol', 'Unknown')}(${data.get('calculated_value', 0):.1f})"
                        for data in self.monitored_tokens.values()
                    ]
                    logger.info(f"PortfolioMonitor: 当前监控列表: {', '.join(monitored_symbols)}")
            
        except Exception as e:
            logger.error(f"PortfolioMonitor: 更新监控状态时出错: {e}")
    
    def stop_monitoring(self):
        """停止所有监控"""
        try:
            logger.info(f"PortfolioMonitor: 停止所有监控...")
            
            # 🔥 停止状态更新定时器
            if self.status_timer.isActive():
                self.status_timer.stop()
            
            # 停止所有HeadlessChartWidget
            for token_address in list(self.headless_widgets.keys()):
                self.remove_token_monitoring(token_address)
            
            # 重置状态
            self.is_active = False
            self.total_monitored = 0
            self.active_monitored = 0
            
            # 发射状态更新
            self.monitoring_status_changed.emit("Portfolio监控已停止", 0, 0)
            
            logger.info(f"PortfolioMonitor: 所有监控已停止")
            
        except Exception as e:
            logger.error(f"PortfolioMonitor: 停止监控时出错: {e}")
    
    def change_strategy(self, strategy_name: str):
        """
        更改所有监控代币的策略
        
        参数:
            strategy_name (str): 新的策略名称
        """
        try:
            logger.info(f"PortfolioMonitor: 更改监控策略为 '{strategy_name}'")
            
            self.default_strategy = strategy_name
            
            # 更新所有HeadlessChartWidget的策略
            for token_address, widget in self.headless_widgets.items():
                token_data = self.monitored_tokens[token_address]
                token_data['strategy_name'] = strategy_name
                widget.on_strategy_changed(strategy_name)
            
            logger.info(f"PortfolioMonitor: 已为 {len(self.headless_widgets)} 个代币更新策略")
            
        except Exception as e:
            logger.error(f"PortfolioMonitor: 更改策略时出错: {e}")
    
    def change_timeframe(self, timeframe: str):
        """
        更改所有监控代币的时间周期
        
        参数:
            timeframe (str): 新的时间周期
        """
        try:
            logger.info(f"PortfolioMonitor: 更改监控时间周期为 '{timeframe}'")
            
            self.default_timeframe = timeframe
            
            # 更新所有HeadlessChartWidget的时间周期
            for token_address, widget in self.headless_widgets.items():
                token_data = self.monitored_tokens[token_address]
                token_data['timeframe'] = timeframe
                widget.on_timeframe_changed(timeframe)
            
            logger.info(f"PortfolioMonitor: 已为 {len(self.headless_widgets)} 个代币更新时间周期")
            
        except Exception as e:
            logger.error(f"PortfolioMonitor: 更改时间周期时出错: {e}")
    
    def get_monitoring_summary(self) -> Dict:
        """
        获取监控摘要信息
        
        返回:
            Dict: 监控摘要
        """
        try:
            summary = {
                'is_active': self.is_active,
                'total_monitored': self.total_monitored,
                'active_monitored': self.active_monitored,
                'strategy': self.default_strategy,
                'timeframe': self.default_timeframe,
                'min_value': self.min_token_value,
                'tokens': []
            }
            
            for token_address, token_data in self.monitored_tokens.items():
                widget = self.headless_widgets.get(token_address)
                has_data = widget and widget.df is not None and not widget.df.empty
                
                token_summary = {
                    'symbol': token_data.get('symbol', 'Unknown'),
                    'address': token_address,
                    'value': token_data.get('calculated_value', 0),
                    'has_data': has_data,
                    'is_risk': token_data.get('is_risk_token', False)
                }
                summary['tokens'].append(token_summary)
            
            return summary
            
        except Exception as e:
            logger.error(f"PortfolioMonitor: 获取监控摘要时出错: {e}")
            return {
                'is_active': False,
                'total_monitored': 0,
                'active_monitored': 0,
                'strategy': 'Unknown',
                'timeframe': 'Unknown',
                'min_value': 0,
                'tokens': []
            }
    
    def __del__(self):
        """析构函数"""
        try:
            logger.debug("PortfolioMonitor: 删除实例")
            self.stop_monitoring()
        except Exception as e:
            logger.debug(f"PortfolioMonitor: 删除实例时出错: {e}") 